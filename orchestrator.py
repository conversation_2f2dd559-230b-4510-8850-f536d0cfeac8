from openai import OpenAI
import json

# Connect to LM Studio local API
client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

# System instruction for Qwen
system_msg = {
    "role": "system",
    "content": "You are an assistant that can call tools when needed."
}

# Tool definitions for Qwen
tools = [
    {
        "type": "function",
        "function": {
            "name": "add_numbers",
            "description": "Add two integers and return the sum",
            "parameters": {
                "type": "object",
                "properties": {
                    "a": {"type": "integer"},
                    "b": {"type": "integer"}
                },
                "required": ["a", "b"]
            }
        }
    }
]

def add_numbers(a, b):
    return a + b

# Ask the user
user_input = input("User: ")
user_msg = {"role": "user", "content": user_input}

# Step 1: Qwen initial response (may contain tool call)
resp = client.chat.completions.create(
    model="qwen-4b",
    messages=[system_msg, user_msg],
    tools=tools
)

msg = resp.choices[0].message

# Step 2: If <PERSON><PERSON> calls a tool, run it and feed result back
if msg.tool_calls:
    tool_msgs = [system_msg, user_msg, msg]  # conversation history

    for call in msg.tool_calls:
        tool_name = call.function.name
        args = json.loads(call.function.arguments)

        if tool_name == "add_numbers":
            result = add_numbers(args["a"], args["b"])

        # Send result back to Qwen
        tool_result_msg = {
            "role": "tool",
            "tool_call_id": call.id,
            "content": json.dumps({"result": result})
        }
        tool_msgs.append(tool_result_msg)

        # Step 3: Qwen final answer using tool output
        final_resp = client.chat.completions.create(
            model="qwen-4b",
            messages=tool_msgs
        )

        print("Assistant:", final_resp.choices[0].message.content)
else:
    # No tool call — just output Qwen's text
    print("Assistant:", msg.content)
